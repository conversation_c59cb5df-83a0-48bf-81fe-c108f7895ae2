
<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <title>Unity WebGL Player | public</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="TemplateData/style.css">
  </head>
  <body>
    <div id="unity-container" class="unity-desktop">
      <canvas id="unity-canvas" width=1080 height=1890></canvas>
      <div id="unity-loading-bar">
        <div id="unity-logo"></div>
        <div id="unity-progress-bar-empty">
          <div id="unity-progress-bar-full"></div>
        </div>
      </div>
      <div id="unity-warning"> </div>
      <!-- 实时显示canvase大小
      <div id="canvasSizeDisplay" style="position:fixed;top:40px;right:40px;background:rgba(0,0,0,0.7);color:#fff;padding:6px 12px;border-radius:5px;font-family:monospace;z-index:9999;">
        Canvas size: 
      </div>-->
    </div>
    <script>
      var container = document.querySelector("#unity-container");
      var canvas = document.querySelector("#unity-canvas");
      var loadingBar = document.querySelector("#unity-loading-bar");
      var progressBarFull = document.querySelector("#unity-progress-bar-full");
      var warningBanner = document.querySelector("#unity-warning");

      // Shows a temporary message banner/ribbon for a few seconds, or
      // a permanent error message on top of the canvas if type=='error'.
      // If type=='warning', a yellow highlight color is used.
      // Modify or remove this function to customize the visually presented
      // way that non-critical warnings and error messages are presented to the
      // user.
      function unityShowBanner(msg, type) {
        function updateBannerVisibility() {
          warningBanner.style.display = warningBanner.children.length ? 'block' : 'none';
        }
        var div = document.createElement('div');
        div.innerHTML = msg;
        warningBanner.appendChild(div);
        if (type == 'error') div.style = 'background: red; padding: 10px;';
        else {
          if (type == 'warning') div.style = 'background: yellow; padding: 10px;';
          setTimeout(function() {
            warningBanner.removeChild(div);
            updateBannerVisibility();
          }, 5000);
        }
        updateBannerVisibility();
      }

      var buildUrl = "Build";
      var loaderUrl = buildUrl + "/webhezi.loader.js";
      var config = {
        dataUrl: buildUrl + "/webhezi.data",
        frameworkUrl: buildUrl + "/webhezi.framework.js",
        codeUrl: buildUrl + "/webhezi.wasm",
        streamingAssetsUrl: "StreamingAssets",
        companyName: "4399",
        productName: "public",
        productVersion: "1.0",
        showBanner: unityShowBanner,
      };

      // By default Unity keeps WebGL canvas render target size matched with
      // the DOM size of the canvas element (scaled by window.devicePixelRatio)
      // Set this to false if you want to decouple this synchronization from
      // happening inside the engine, and you would instead like to size up
      // the canvas DOM size and WebGL render target sizes yourself.
      // config.matchWebGLToCanvasSize = false;

      var isMobile = /iPhone|iPad|iPod|Android|Harmony/i.test(navigator.userAgent);

      function setCanvasSize(canvas) {
        if (!(canvas instanceof HTMLCanvasElement)) {
            console.error("传入的不是一个 canvas 元素！");
            return;
        }

        var ratio = isMobile ? 1 : 4 / 7;

        var width = window.innerWidth;
        var height = window.innerHeight;

        canvas.width = width * ratio;   // 设置实际绘图区域大小
        canvas.height = height;
        canvas.style.width = width + "px";   // 设置显示大小（可选，防止拉伸）
        canvas.style.height = height + "px";

        console.log('Canvas SetSize: ' + width + ' - ' + height + ' : '+ window.innerWidth + ' - ' + window.innerHeight + '  ratio:' + ratio);
      }

      if (isMobile) {
        // Mobile device style: fill the whole browser client area with the game canvas:

        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes';
        document.getElementsByTagName('head')[0].appendChild(meta);
        container.className = "unity-mobile";

        // To lower canvas resolution on mobile devices to gain some
        // performance, uncomment the following line:
        config.devicePixelRatio = 2;
        setCanvasSize(canvas)

        //unityShowBanner('WebGL builds are not supported on mobile devices.');
      } else {
        // Desktop style: Render the game canvas in a window that can be maximized to fullscreen:

        setCanvasSize(canvas)
      }

      loadingBar.style.display = "block";

      var script = document.createElement("script");
      script.src = loaderUrl;
      script.onload = () => {
        createUnityInstance(canvas, config, (progress) => {
          progressBarFull.style.width = 100 * progress + "%";
        }).then((unityInstance) => {
          loadingBar.style.display = "none";
          window.FNHFGameGlobal = unityInstance;
        }).catch((message) => {
          alert(message);
        });
      };
      document.body.appendChild(script);

      function resizeCanvas() {
        var canvas = document.querySelector("#unity-canvas");
        if (!canvas) { console.error('canvas not found'); return; }
		    //var dpr = window.devicePixelRatio || 1;
        setCanvasSize(canvas);

        //实时显示canvase大小
        //var display = document.getElementById('canvasSizeDisplay');
        //display.textContent = 'Canvas size: ' + canvas.width + ' x ' + canvas.height;
      }
      resizeCanvas();
      window.addEventListener('resize', resizeCanvas);
    </script>
  </body>
</html>
